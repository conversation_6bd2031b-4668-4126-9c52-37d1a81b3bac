import {CheckOutlined, PlusOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, Highlighter, InputCellTable, ModalQuanLyFileCaNhan} from "@src/components";
import {Flex, Modal, Table, TableColumnType, Tag} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useQuanLyDoiTacContext} from "../index.context";
import {LogoProps, IModalChonLogoRef, TableLogoColumnDataType, tableLogoColumn, TableLogoColumnDataIndex} from "./Constant";
import "../index.default.scss";
import {env} from "@src/utils";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";

const ModalChonLogoComponent = forwardRef<IModalChonLogoRef, LogoProps>(({chiTietDoiTac, onClickChonFileSuccess}: LogoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (data?: CommonExecute.Execute.IChiTietDoiTac) => {
      setIsOpen(true);
      //   if (dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [isOpen, setIsOpen] = useState(false);
  const {loading, danhSachLogo} = useQuanLyDoiTacContext();
  //   const [form] = Form.useForm();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  //   const formValues = Form.useWatch([], form);
  const modalQuanLyFileRef = useRef<any>(null);
  const [imageUrl_app, setImageUrl_app] = useState<string | null>(null);
  const [imageUrl_web, setImageUrl_web] = useState<string | null>(null);
  const [fileSelected, setFileSelected] = useState<any>(null);
  const [loaiLogo, setLoaiLogo] = useState<string>("");
  const [searchTextTable, setSearchTextTable] = useState<string>("");
  const [dataSource, setDataSource] = useState<Array<TableLogoColumnDataType>>([]);
  const [searchedColumn, setSearchedColumn] = useState<TableLogoColumnDataIndex | "">(""); //key column đang được search
  const [inputRowKey, setInputRowKey] = useState<string | null>(null);
  const [inputRowKeys, setInputRowKeys] = useState<string[]>([]);
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setFileSelected(null);
    onClickChonFileSuccess?.([]);
    // setchiTietDoiTac(null);
    // form.resetFields();
  }, []);
  //   console.log("chiTietDoiTac.url_file", chiTietDoiTac.url_file);
  useEffect(() => {
    if (chiTietDoiTac?.id_file) {
      console.log("chiTietDoiTac.url_file", chiTietDoiTac.url_file);
      if (chiTietDoiTac.loai === "LOGO_APP") {
        setImageUrl_app(env.VITE_BASE_URL + chiTietDoiTac.url_file);
      } else if (chiTietDoiTac.loai === "LOGO_WEB") {
        setImageUrl_web(env.VITE_BASE_URL + chiTietDoiTac.url_file);
      }
      //   setImageUrl(env.VITE_BASE_URL + chiTietDoiTac.url_file);
    } else {
      if (chiTietDoiTac.loai === "LOGO_APP") {
        setImageUrl_app(null);
      } else if (chiTietDoiTac.loai === "LOGO_WEB") {
        setImageUrl_web(null);
      }
    }
  }, [chiTietDoiTac]);

  const dataTableLogo = useMemo<Array<TableLogoColumnDataType>>(() => {
    try {
      const tableData = danhSachLogo.map(item => {
        return {
          key: item.id_file,
          id_file: item.id_file,
          ma_doi_tac: item.ma_doi_tac,
          url_file: item.url_file,
          extension: item.extension,
          loai: item.loai,
          hanh_dong: () => renderChonFile(item.loai),
        };
      });
      const arrEmptyRow: Array<TableLogoColumnDataType> = fillRowTableEmpty(tableData.length, defaultTableProps.defaultPageSize);
      setDataSource([...tableData, ...arrEmptyRow]);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDonViChiNhanh error", error);
      return [];
    }
  }, [danhSachLogo]);
  const normalizeInput = (value: string): string => {
    // Chuyển thành chữ in hoa và loại bỏ dấu cách
    let normalized = value.toUpperCase().replace(/\s/g, "");

    // Loại bỏ dấu tiếng Việt
    normalized = normalized
      .normalize("NFD") // Chuẩn hóa dạng phân tách dấu
      .replace(/[\u0300-\u036f]/g, "") // Xóa các ký tự dấu
      .replace(/đ/g, "D") // Thay 'đ' bằng 'D'
      .replace(/Đ/g, "D"); // Thay 'Đ' bằng 'D'

    return normalized;
  };
  const handleInputChange = (index: number, dataIndex: string, value: string) => {
    setDataSource(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};
      if (dataIndex === "loai") {
        console.log("value", value);
        const normalizedValue = dataIndex === "loai" ? normalizeInput(value) : value;
        next[index] = {...next[index], [dataIndex]: normalizedValue};
      }
      return next;
    });
  };
  //Bấm Chọn - truyền fileSelected lên modal cha
  const handleChonFileClick = useCallback(() => {
    try {
      if (fileSelected && onClickChonFileSuccess) {
        // Truyền fileSelected dưới dạng array để phù hợp với interface
        onClickChonFileSuccess([fileSelected]);
        closeModal();
        console.log("Đã truyền fileSelected lên modal cha:", fileSelected);
      } else {
        console.warn("Chưa chọn file hoặc không có callback function");
      }
    } catch (error) {
      console.log("handleChonFileClick error", error);
    }
  }, [fileSelected, onClickChonFileSuccess]);
  const handleChonFile = useCallback((loai: string) => {
    if (loai === "LOGO_APP") {
      setLoaiLogo("LOGO_APP");
    } else if (loai === "LOGO_WEB") {
      setLoaiLogo("LOGO_WEB");
    }
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  }, []);
  const handleChonFileSuccess = useCallback(
    (filesSelected: any[]) => {
      console.log("handleChonFileSuccess", filesSelected);
      if (filesSelected && filesSelected.length > 0) {
        const selectedFile = filesSelected[0];
        const fileId = selectedFile.id;
        console.log("selectedFile", selectedFile);
        setFileSelected(selectedFile);
        if (fileId) {
          //hiển thị hình ảnh
          if (selectedFile.url_file && selectedFile.extension) {
            const imageExtensions = [".jpg", ".jpeg", ".png", ".gif"];
            if (imageExtensions.includes(selectedFile.extension.toLowerCase())) {
              const imageUrl = env.VITE_BASE_URL + selectedFile.url_file;
              console.log("loaiLogo", loaiLogo);
              if (loaiLogo === "LOGO_APP") {
                setImageUrl_app(imageUrl);
              } else if (loaiLogo === "LOGO_WEB") {
                setImageUrl_web(imageUrl);
              }
              // if (selectedFile.loai === "LOGO_APP") {
              //   setImageUrl_app(imageUrl);
              // } else if (selectedFile.loai === "LOGO_WEB") {
              //   setImageUrl_web(imageUrl);
              // }
              // setImageUrl(imageUrl);
            }
          }

          if (modalQuanLyFileRef.current?.close) {
            modalQuanLyFileRef.current.close();
          }
        }
      }
    },
    [loaiLogo],
  );

  const handleAddRow = () => {
    // Tìm index dòng trống đầu tiên
    const emptyRowIndex = dataSource.findIndex(item => item.key.includes("empty"));
    const newData = [...dataSource];
    if (emptyRowIndex !== -1) {
      newData.splice(emptyRowIndex, 1);
    }
    // Tìm vị trí cuối cùng của dữ liệu gốc (dòng có ma_cau_hoi hoặc trường phân biệt)
    const lastDataIndex = newData.reduce((lastIdx, item, idx) => (item.loai ? idx : lastIdx), -1);
    // Tạo dòng dữ liệu mới trống
    const newKey = `new-${Date.now()}`;
    const newRow = {
      key: newKey,
      loai: "",
      hanh_dong: () => renderChonFile(""),
      url_file: "",
    };

    // Thêm dòng mới vào ngay sau dòng dữ liệu cuối cùng
    newData.splice(lastDataIndex + 1, 0, newRow);

    setDataSource(newData);
    setInputRowKey(newKey);
    setInputRowKeys(prev => [...prev, newKey]);
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableLogoColumnDataIndex, title: string): TableColumnType<TableLogoColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */

    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */

    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "loai") {
        if (record.key.toString().includes("empty")) return "";
        return text;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  // RENDER
  const renderColumn = (item: TableColumnType<TableLogoColumnDataType>) => {
    if (item.dataIndex === "loai") {
      return {
        ...item,
        render: (value: any, record: TableLogoColumnDataType, index: number) => {
          if (record.key.toString().includes("empty")) return "";
          if (inputRowKeys.includes(record.key)) {
            return (
              <div className="custom-checkbox-cell">
                <InputCellTable
                  className="text-left"
                  component="input"
                  key={`${record.key}-${item.dataIndex as keyof TableLogoColumnDataType}`}
                  value={record[item.dataIndex as keyof TableLogoColumnDataType] ?? ""}
                  index={index}
                  dataIndex={item.dataIndex as keyof TableLogoColumnDataType}
                  onChange={handleInputChange}
                  autoFocus
                />
                {/* <span className="ml-1">%</span> */}
              </div>
            );
          }
          return value;
        },
      };
    }
    if (item.dataIndex === "hanh_dong") {
      return {
        ...item,
        render: (value: any, record: TableLogoColumnDataType, index: number) => {
          if (record.key.toString().includes("empty")) return "";
          renderChonFile(record.loai);
          return value;
        },
      };
    }
    return {
      ...item,
      ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as TableLogoColumnDataIndex, item.title as string) : {}),
    };
  };
  const renderChonFile = (loai: string) => {
    console.log("loai", loai);
    return (
      <div
        onClick={() => handleChonFile(loai)}
        style={{
          width: 150,
          height: 150,
          border: "2px dashed #d9d9d9",
          borderRadius: 8,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          overflow: "hidden",
          background: "#fafafa",
        }}>
        {imageUrl ? <img src={imageUrl} alt="selected" style={{width: "100%", height: "100%", objectFit: "cover"}} /> : <span style={{color: "#999"}}>+</span>}
      </div>
    );
  };
  const renderTable = () => {
    return (
      <Table<TableLogoColumnDataType>
        {...defaultTableProps}
        dataSource={dataSource} //mảng dữ liệu record được hiển thị
        columns={(tableLogoColumn || []).map(renderColumn)} //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={false}
        title={null}
        scroll={{y: 200}}
      />
    );
  };
  //FOOTER
  const renderFooter = () => {
    return (
      <div className="modal-footer-spaced">
        <Button type="primary" onClick={() => handleAddRow()} className="float-left mr-2" icon={<PlusOutlined />}>
          Thêm loại
        </Button>
        <Button type="primary" onClick={handleChonFileClick} className="mr-2" icon={<CheckOutlined />} iconPosition="end">
          Lưu
        </Button>
      </div>
    );
  };

  //Render
  return (
    <Flex vertical gap="" align="center">
      <Modal
        title="Chọn Logo"
        // centered
        className="modal-chon-logo"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "40vw",
          sm: "40vw",
          md: "40vw",
          lg: "40vw",
          xl: "40vw",
          xxl: "40vw",
        }}
        // style={{
        //   top: 30,
        // }}
        styles={{
          body: {
            paddingTop: "16px",
            // paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderTable()}
        <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} />
      </Modal>
    </Flex>
  );
});

ModalChonLogoComponent.displayName = "ModalChonLogoComponent";
export const ModalChonLogo = memo(ModalChonLogoComponent, isEqual);
