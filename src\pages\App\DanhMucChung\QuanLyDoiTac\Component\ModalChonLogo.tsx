import {CheckOutlined, PlusOutlined} from "@ant-design/icons";
import {<PERSON><PERSON>, <PERSON>lighter, InputCellTable, ModalQuanLyFileCaNhan} from "@src/components";
import {Flex, Modal, Table, TableColumnType, Tag} from "antd";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useQuanLyDoiTacContext} from "../index.context";
import {LogoProps, IModalChonLogoRef, TableLogoColumnDataType, tableLogoColumn, TableLogoColumnDataIndex} from "./Constant";
import "../index.default.scss";
import {env} from "@src/utils";
import {defaultTableProps, fillRowTableEmpty} from "@src/hooks";

const ModalChonLogoComponent = forwardRef<IModalChonLogoRef, LogoProps>(({chiTietDoiTac, onClickChonFileSuccess}: LogoProps, ref) => {
  useImperativeHandle(ref, () => ({
    open: (data?: CommonExecute.Execute.IChiTietDoiTac) => {
      setIsOpen(true);
      //   if (dataDonViThuHo); // nếu có dữ liệu -> set chi tiết DonViThuHo -> là sửa
    },
    close: () => setIsOpen(false),
  }));
  const [isOpen, setIsOpen] = useState(false);
  const {loading, danhSachLogo} = useQuanLyDoiTacContext();
  const [disableSubmit, setDisableSubmit] = useState<boolean>(false);
  const modalQuanLyFileRef = useRef<any>(null);
  // Thay đổi: Lưu trữ file được chọn theo từng record với loai và filesSelected
  const [filesSelectedByRecord, setFilesSelectedByRecord] = useState<{[key: string]: {loai: string; filesSelected: any[]}}>({});
  const [currentRecordKey, setCurrentRecordKey] = useState<string>(""); // Record đang được chọn file
  const [forceRender, setForceRender] = useState<number>(0); // Force re-render counter
  const [loaiLogo, setLoaiLogo] = useState<string>("");
  const [searchTextTable, setSearchTextTable] = useState<string>("");
  const [dataSource, setDataSource] = useState<Array<TableLogoColumnDataType>>([]);
  const [searchedColumn, setSearchedColumn] = useState<TableLogoColumnDataIndex | "">(""); //key column đang được search
  const [inputRowKey, setInputRowKey] = useState<string | null>(null);
  const [inputRowKeys, setInputRowKeys] = useState<string[]>([]);
  const closeModal = useCallback(() => {
    setIsOpen(false);
    setFilesSelectedByRecord({}); // Reset files selected
    onClickChonFileSuccess?.([]);
    // setchiTietDoiTac(null);
    // form.resetFields();
  }, []);
  //   console.log("chiTietDoiTac.url_file", chiTietDoiTac.url_file);
  //   useEffect(() => {
  //     if (chiTietDoiTac?.id_file) {
  //       console.log("chiTietDoiTac.url_file", chiTietDoiTac.url_file);
  //       if (chiTietDoiTac.loai === "LOGO_APP") {
  //         setImageUrl_app(env.VITE_BASE_URL + chiTietDoiTac.url_file);
  //       } else if (chiTietDoiTac.loai === "LOGO_WEB") {
  //         setImageUrl_web(env.VITE_BASE_URL + chiTietDoiTac.url_file);
  //       }
  //       //   setImageUrl(env.VITE_BASE_URL + chiTietDoiTac.url_file);
  //     } else {
  //       if (chiTietDoiTac.loai === "LOGO_APP") {
  //         setImageUrl_app(null);
  //       } else if (chiTietDoiTac.loai === "LOGO_WEB") {
  //         setImageUrl_web(null);
  //       }
  //     }
  //   }, [chiTietDoiTac]);

  // Force re-render when filesSelectedByRecord changes
  //   useEffect(() => {
  //     console.log("filesSelectedByRecord changed:", filesSelectedByRecord);
  //   }, [filesSelectedByRecord]);

  //   console.log("filesSelectedByRecord", filesSelectedByRecord);
  const dataTableLogo = useMemo<Array<TableLogoColumnDataType>>(() => {
    console.log("dataTableLogo useMemo triggered, filesSelectedByRecord:", filesSelectedByRecord);
    console.log("danhSachLogo", danhSachLogo);
    try {
      const tableData = danhSachLogo.map(item => {
        // Lấy file đã được chọn cho record này (nếu có)
        console.log("=== Processing item in useMemo ===");
        console.log("Item:", item);
        console.log("filesSelectedByRecord trong bảng :", filesSelectedByRecord);
        const itemKey = item.id_file.toString();
        console.log(`Looking for key: "${itemKey}" in Object with keys:`, Object.keys(filesSelectedByRecord));
        const selectedRecord = filesSelectedByRecord[itemKey];
        console.log(`Selected record for key "${itemKey}":`, selectedRecord);
        const selectedFile = selectedRecord?.filesSelected?.[0]; // Lấy file đầu tiên từ filesSelected
        console.log(`Selected file:`, selectedFile);

        // Log final URL that will be used
        const finalUrl = selectedFile?.url_file || item.url_file;
        console.log(`Final URL for item ${item.id_file}: ${finalUrl}`);
        return {
          key: item.id_file.toString(), // Đảm bảo key là string
          id_file: item.id_file,
          ma_doi_tac: item.ma_doi_tac,
          url_file: selectedFile?.url_file || item.url_file, // Ưu tiên file đã chọn
          extension: selectedFile?.extension || item.extension,
          loai: selectedRecord?.loai || item.loai, // Ưu tiên loai đã chọn
          hanh_dong: undefined, // Sẽ được xử lý trong renderColumn
        };
      });
      const arrEmptyRow: Array<TableLogoColumnDataType> = fillRowTableEmpty(tableData.length, defaultTableProps.defaultPageSize);
      setDataSource([...tableData, ...arrEmptyRow]);
      return [...tableData, ...arrEmptyRow];
    } catch (error) {
      console.log("dataTableListDonViChiNhanh error", error);
      return [];
    }
  }, [danhSachLogo, filesSelectedByRecord, forceRender]);
  const normalizeInput = (value: string): string => {
    // Chuyển thành chữ in hoa và loại bỏ dấu cách
    let normalized = value.toUpperCase().replace(/\s/g, "");

    // Loại bỏ dấu tiếng Việt
    normalized = normalized
      .normalize("NFD") // Chuẩn hóa dạng phân tách dấu
      .replace(/[\u0300-\u036f]/g, "") // Xóa các ký tự dấu
      .replace(/đ/g, "D") // Thay 'đ' bằng 'D'
      .replace(/Đ/g, "D"); // Thay 'Đ' bằng 'D'

    return normalized;
  };
  const handleInputChange = (index: number, dataIndex: string, value: string) => {
    setDataSource(prev => {
      const next = [...prev];
      next[index] = {...next[index], [dataIndex]: value};
      if (dataIndex === "loai") {
        console.log("value", value);
        const normalizedValue = dataIndex === "loai" ? normalizeInput(value) : value;
        next[index] = {...next[index], [dataIndex]: normalizedValue};
      }
      return next;
    });
  };
  //Bấm Chọn - truyền tất cả files đã chọn lên modal cha
  const handleChonFileClick = useCallback(() => {
    try {
      if (Object.keys(filesSelectedByRecord).length > 0 && onClickChonFileSuccess) {
        console.log("filesSelectedByRecord", filesSelectedByRecord);
        // Truyền tất cả files đã chọn dưới dạng array - chỉ lấy filesSelected
        const allSelectedFiles: any[] = [];
        for (const [key, record] of Object.entries(filesSelectedByRecord)) {
          if (record.filesSelected && record.filesSelected.length > 0) {
            allSelectedFiles.push(...record.filesSelected);
          }
        }
        onClickChonFileSuccess(allSelectedFiles);
        closeModal();
        console.log("Đã truyền files đã chọn lên modal cha:", allSelectedFiles);
      } else {
        console.warn("Chưa chọn file hoặc không có callback function");
      }
    } catch (error) {
      console.log("handleChonFileClick error", error);
    }
  }, [filesSelectedByRecord, onClickChonFileSuccess, closeModal]);
  const handleChonFile = useCallback((loai: string, recordKey: string) => {
    console.log("handleChonFile called with:", {loai, recordKey});
    setLoaiLogo(loai);
    // Đảm bảo key luôn là string của id_file
    const normalizedKey = recordKey.toString();
    setCurrentRecordKey(normalizedKey);
    console.log("Set currentRecordKey to:", normalizedKey);
    if (modalQuanLyFileRef.current?.open) {
      modalQuanLyFileRef.current.open();
    }
  }, []);
  const handleChonFileSuccess = useCallback(
    (filesSelected: any[]) => {
      console.log("handleChonFileSuccess", filesSelected);
      console.log("currentRecordKey", currentRecordKey);
      if (filesSelected && filesSelected.length > 0 && currentRecordKey) {
        const selectedFile = filesSelected[0];
        const fileId = selectedFile.id;
        console.log("selectedFile", selectedFile);

        // Lưu file đã chọn cho record hiện tại với loai và filesSelected
        setFilesSelectedByRecord(prev => {
          const newObj = {
            ...prev,
            [currentRecordKey]: {
              loai: loaiLogo,
              filesSelected: filesSelected,
            },
          };
          console.log("Saved with key:", currentRecordKey);
          console.log("Updated filesSelectedByRecord:", newObj);
          console.log("Object keys:", Object.keys(newObj));
          return newObj;
        });

        // Force re-render để trigger useMemo
        setForceRender(prev => {
          const newValue = prev + 1;
          console.log("Force render triggered:", newValue);
          return newValue;
        });

        if (fileId) {
          //hiển thị hình ảnh cho loại logo tương ứng
          if (selectedFile.url_file && selectedFile.extension) {
            const imageExtensions = [".jpg", ".jpeg", ".png", ".gif"];
            if (imageExtensions.includes(selectedFile.extension.toLowerCase())) {
              const imageUrl = env.VITE_BASE_URL + selectedFile.url_file;
            }
          }

          if (modalQuanLyFileRef.current?.close) {
            modalQuanLyFileRef.current.close();
          }
        }
      }
    },
    [loaiLogo, currentRecordKey],
  );

  const handleAddRow = () => {
    // Tìm index dòng trống đầu tiên
    const emptyRowIndex = dataSource.findIndex(item => item.key.includes("empty"));
    const newData = [...dataSource];
    if (emptyRowIndex !== -1) {
      newData.splice(emptyRowIndex, 1);
    }
    // Tìm vị trí cuối cùng của dữ liệu gốc (dòng có ma_cau_hoi hoặc trường phân biệt)
    const lastDataIndex = newData.reduce((lastIdx, item, idx) => (item.loai ? idx : lastIdx), -1);
    // Tạo dòng dữ liệu mới trống
    const newKey = `new-${Date.now()}`;
    const newRow = {
      key: newKey,
      loai: "",
      hanh_dong: undefined, // Sẽ được xử lý trong renderColumn
      url_file: "",
    };

    // Thêm dòng mới vào ngay sau dòng dữ liệu cuối cùng
    newData.splice(lastDataIndex + 1, 0, newRow);

    setDataSource(newData);
    setInputRowKey(newKey);
    setInputRowKeys(prev => [...prev, newKey]);
  };

  // dataIndex : là các key của column, title : tiêu đề của column
  const getColumnSearchProps = (dataIndex: TableLogoColumnDataIndex, title: string): TableColumnType<TableLogoColumnDataType> => ({
    /**
     *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
     * @param param0
     * @returns
     * dataIndex !== "trang_thai_ten"
     */

    onFilter: (value, record) => {
      return (
        record[dataIndex]
          ?.toString()
          .toLowerCase()
          .includes((value as string).toLowerCase()) || false
      );
    },
    /**
     * filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
     */

    render: (
      text,
      record,
      //  index
    ) => {
      if (dataIndex === "loai") {
        if (record.key.toString().includes("empty")) return "";
        return text;
      }
      return searchedColumn === dataIndex ? (
        <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} />
      ) : text !== undefined ? (
        text
      ) : (
        <Tag color={"transparent"} className="!text-white text-[11px]">
          {"\u00A0"}
        </Tag>
      );
    },
  });
  // RENDER
  const renderColumn = (item: TableColumnType<TableLogoColumnDataType>) => {
    if (item.dataIndex === "loai") {
      return {
        ...item,
        render: (value: any, record: TableLogoColumnDataType, index: number) => {
          if (record.key.toString().includes("empty")) return "";
          if (inputRowKeys.includes(record.key)) {
            return (
              <div className="custom-checkbox-cell">
                <InputCellTable
                  className="text-left"
                  component="input"
                  key={`${record.key}-${item.dataIndex as keyof TableLogoColumnDataType}`}
                  value={record[item.dataIndex as keyof TableLogoColumnDataType] ?? ""}
                  index={index}
                  dataIndex={item.dataIndex as keyof TableLogoColumnDataType}
                  onChange={handleInputChange}
                  autoFocus
                />
                {/* <span className="ml-1">%</span> */}
              </div>
            );
          }
          return value;
        },
      };
    }
    if (item.dataIndex === "hanh_dong") {
      return {
        ...item,
        render: (value: any, record: TableLogoColumnDataType, index: number) => {
          if (record.key.toString().includes("empty")) return "";
          // Sử dụng id_file làm key thay vì record.key
          const recordKey = record.id_file ? record.id_file.toString() : record.key;
          return renderChonFile(record.loai || "", recordKey, record.url_file);
        },
      };
    }
    // if (item.dataIndex === "url_file") {
    //   return {
    //     ...item,
    //     render: (value: any, record: TableLogoColumnDataType, index: number) => {
    //       if (record.key.toString().includes("empty")) return "";
    //       return value;
    //     },
    //   };
    // }
    return {
      ...item,
      ...(item.key && typeof item.title === "string" && item.key !== "stt" ? getColumnSearchProps(item.key as TableLogoColumnDataIndex, item.title as string) : {}),
    };
  };
  const renderChonFile = (loai: string, recordKey: string, urlFile?: string) => {
    console.log("renderChonFile called with:", {loai, recordKey, urlFile});
    console.log("filesSelectedByRecord in renderChonFile:", filesSelectedByRecord);
    console.log("Available keys in filesSelectedByRecord:", Object.keys(filesSelectedByRecord));

    // Xác định imageUrl cho record cụ thể
    let displayImageUrl = null;

    // Đảm bảo recordKey là string
    const normalizedRecordKey = recordKey.toString();
    console.log("Looking for normalized key:", normalizedRecordKey);

    // Ưu tiên file đã được chọn từ filesSelectedByRecord
    let selectedRecord = filesSelectedByRecord[normalizedRecordKey];
    console.log("Direct lookup result:", selectedRecord);

    // Nếu không tìm thấy, thử tìm theo tất cả các key trong Object
    if (!selectedRecord) {
      console.log("Direct lookup failed, trying fuzzy search...");
      for (const [key, record] of Object.entries(filesSelectedByRecord)) {
        console.log(`Comparing key "${key}" with recordKey "${normalizedRecordKey}"`);
        // Có thể key được lưu khác với recordKey hiện tại
        if (key === normalizedRecordKey || key.includes(normalizedRecordKey) || normalizedRecordKey.includes(key)) {
          console.log("Found match with key:", key);
          selectedRecord = record;
          break;
        }
      }
    }

    console.log("Final selectedRecord for recordKey", normalizedRecordKey, ":", selectedRecord);

    // Lấy file đầu tiên từ filesSelected
    const selectedFile = selectedRecord?.filesSelected?.[0];
    console.log("selectedFile:", selectedFile);

    if (selectedFile?.url_file) {
      displayImageUrl = env.VITE_BASE_URL + selectedFile.url_file;
      console.log("Using selected file URL:", displayImageUrl);
    } else if (urlFile) {
      // Fallback về url_file gốc từ data
      displayImageUrl = env.VITE_BASE_URL + urlFile;
      console.log("Using original URL:", displayImageUrl);
    }

    console.log("Final displayImageUrl:", displayImageUrl);

    return (
      <div
        onClick={() => handleChonFile(loai, recordKey)}
        style={{
          width: 150,
          height: 150,
          border: "2px dashed #d9d9d9",
          borderRadius: 8,
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          cursor: "pointer",
          overflow: "hidden",
          background: "#fafafa",
        }}>
        {displayImageUrl ? <img src={displayImageUrl} alt="selected" style={{width: "100%", height: "100%", objectFit: "cover"}} /> : <span style={{color: "#999"}}>+</span>}
      </div>
    );
  };
  const renderTable = () => {
    return (
      <Table<TableLogoColumnDataType>
        {...defaultTableProps}
        dataSource={dataSource} //mảng dữ liệu record được hiển thị
        columns={(tableLogoColumn || []).map(renderColumn)} //định nghĩa cột của table
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={false}
        title={null}
        scroll={{y: 400}}
      />
    );
  };
  //FOOTER
  const renderFooter = () => {
    return (
      <div className="modal-footer-spaced">
        <Button type="primary" onClick={() => handleAddRow()} className="float-left mr-2" icon={<PlusOutlined />}>
          Thêm loại
        </Button>
        <Button type="primary" onClick={handleChonFileClick} className="mr-2" icon={<CheckOutlined />} iconPosition="end">
          Lưu
        </Button>
      </div>
    );
  };

  //Render
  return (
    <Flex vertical gap="" align="center">
      <Modal
        title="Chọn Logo"
        // centered
        className="modal-chon-logo"
        maskClosable={false}
        open={isOpen}
        onOk={() => closeModal()}
        onCancel={() => closeModal()}
        width={{
          xs: "40vw",
          sm: "40vw",
          md: "40vw",
          lg: "40vw",
          xl: "40vw",
          xxl: "40vw",
        }}
        style={{
          top: 30,
        }}
        styles={{
          body: {
            paddingTop: "16px",
            // paddingBottom: "16px",
          },
        }}
        footer={renderFooter}>
        {renderTable()}
        <ModalQuanLyFileCaNhan ref={modalQuanLyFileRef} onClickChonFile={handleChonFileSuccess} />
      </Modal>
    </Flex>
  );
});

ModalChonLogoComponent.displayName = "ModalChonLogoComponent";
export const ModalChonLogo = memo(ModalChonLogoComponent, isEqual);
