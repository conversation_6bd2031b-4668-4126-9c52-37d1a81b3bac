import React, {PropsWithChildren, useCallback, useEffect, useMemo, useState} from "react";

import {QuanLyDoiTacContext} from "./index.context";
import {IQuanLyDoiTacContextProps} from "./index.model";
import {ACTION_CODE} from "@src/constants";
import {ReactQuery} from "@src/@types";
import {TableDoiTacColumnDataType} from "./index.configs";
import {useCommonExecute} from "@src/services/react-queries";

const QuanLyDoiTacProvider: React.FC<PropsWithChildren> = props => {
  const {children} = props;
  const mutateUseCommonExecute = useCommonExecute();

  const [listDoiTac, setListDoiTac] = useState<Array<CommonExecute.Execute.IDoiTac>>([]);
  const [tongSoDong, setTongSoDong] = useState<number>(0);
  const [danhSach<PERSON>ogo, setDanhSachLogo] = useState<Array<CommonExecute.Execute.ILogoDoiTac>>([]);
  const [filterParams, setFilterParams] = useState<ReactQuery.ILayDanhSachDoiTacParams & ReactQuery.IPhanTrang>({ma: "", ten: "", mst: "", trang_thai: "", trang: 1, so_dong: 20});
  //khởi tạo dữ liệu ban đàu
  useEffect(() => {
    //nè
    initData();
  }, []);

  const getChiTietDoiTac = useCallback(
    async (data: TableDoiTacColumnDataType) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma: data.key,
          actionCode: ACTION_CODE.GET_CHI_TIET_DOI_TAC,
        });
        return response.data as CommonExecute.Execute.IDoiTac;
      } catch (error) {
        console.log("getChiTietDoiTac err", error);
        return {} as CommonExecute.Execute.IDoiTac;
      }
    },
    [mutateUseCommonExecute],
  );

  const getListDoiTac = useCallback(async () => {
    try {
      const response = await mutateUseCommonExecute.mutateAsync({
        ...filterParams,
        actionCode: ACTION_CODE.TIM_KIEM_DANH_SACH_DOI_TAC_PHAN_TRANG,
      });
      if (response.data) {
        setListDoiTac(response.data.data);
        setTongSoDong(response.data.tong_so_dong);
      }
    } catch (error) {
      console.log("getListDoiTac error ", error);
    }
  }, [mutateUseCommonExecute]);

  useEffect(() => {
    getListDoiTac();
  }, [filterParams]);

  const capNhatChiTietDoiTac = useCallback(
    async (data: ReactQuery.IUpdateDoiTacParams) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ...data,
          actionCode: ACTION_CODE.UPDATE_CHI_TIET_DOI_TAC,
        });
        return response.data;
      } catch (error) {
        console.log("capNhatChiTietDoiTac err", error);
        // return {} as CommonExecute.Execute.IDoiTac;
      }
    },
    [mutateUseCommonExecute],
  );
  const LayDanhSachLogo = useCallback(
    async (ma_doi_tac: string) => {
      try {
        const response = await mutateUseCommonExecute.mutateAsync({
          ma_doi_tac: ma_doi_tac,
          actionCode: ACTION_CODE.LIET_KE_LOGO_DOI_TAC,
        });
        setDanhSachLogo(response.data);
        return response.data;
      } catch (error) {
        console.log("LayDanhSachLogo error ", error);
      }
    },
    [mutateUseCommonExecute],
  );
  //initData chỉ dùng trong useEffect 1 lần, không truyền đi đâu cả, nên không cần memo hoá
  const initData = () => {};

  const value = useMemo<IQuanLyDoiTacContextProps>(
    () => ({
      listDoiTac,
      tongSoDong,
      loading: mutateUseCommonExecute.isLoading,
      filterParams,
      danhSachLogo,
      LayDanhSachLogo,
      getListDoiTac,
      getChiTietDoiTac,
      capNhatChiTietDoiTac,
      setFilterParams,
    }),
    [listDoiTac, tongSoDong, mutateUseCommonExecute, filterParams, danhSachLogo, getListDoiTac, getChiTietDoiTac, capNhatChiTietDoiTac, LayDanhSachLogo],
  );

  return <QuanLyDoiTacContext.Provider value={value}>{children}</QuanLyDoiTacContext.Provider>;
};

export default QuanLyDoiTacProvider;
