import {CheckCircleOutlined, SearchOutlined} from "@ant-design/icons";
import {IFormInput, ReactQuery} from "@src/@types";
import {Button, FormInput, Highlighter} from "@src/components";
import {defaultPaginationTableProps, defaultTableProps, fillRowTableEmpty} from "@src/hooks";
import {Col, Flex, Form, InputRef, Modal, Row, Table, TableColumnType, Tooltip} from "antd";
import {FilterDropdownProps} from "antd/es/table/interface";
import {isEqual} from "lodash";
import {forwardRef, memo, useCallback, useEffect, useImperativeHandle, useMemo, useRef, useState} from "react";
import {useBaoHiemTaiSanContext} from "../../index.context";
import "../../index.default.scss";
import {
  defaultFilterCanBoParams,
  FormTimKiemCanBoKhaiThac,
  IModalTimCanBoKhaiThacRef,
  ModalTimCanBoKhaiThacProps,
  radioItemTrangThaiCanBoTable,
  tableCanBoKhaiThacColumn,
  TableCanBoKhaiThacColumnDataIndex,
  TableCanBoKhaiThacColumnDataType,
} from "./Constant";

const {ma_doi_tac, ma_dly, ma} = FormTimKiemCanBoKhaiThac;

const ModalTimCanBoKhaiThacComponent = forwardRef<IModalTimCanBoKhaiThacRef, ModalTimCanBoKhaiThacProps>(
  ({onSelectCanBoKhaiThac, maDoiTacSelected, daiLyKhaiThacSelected}: ModalTimCanBoKhaiThacProps, ref) => {
    useImperativeHandle(ref, () => ({
      open: canBoSelected => {
        setIsOpen(true);
        initData();
        if (canBoSelected) setChiTietCanBoSelected(canBoSelected);
        console.log("maDoiTacSelected", maDoiTacSelected);
        console.log("daiLyKhaiThacSelected", daiLyKhaiThacSelected);
        // if (daiLyKhaiThacSelected) formTimCanBo.setFieldsValue({ma_dly: daiLyKhaiThacSelected});
      },
      close: () => setIsOpen(false),
    }));

    const {listDoiTac, loading, getListCanBoKhaiThac} = useBaoHiemTaiSanContext();

    // DATA TABLE CÁN BỘ
    const [listCanBoKhaiThac, setListCanBoKhaiThac] = useState<Array<CommonExecute.Execute.ITaiKhoanDaiLy>>([]);
    const [tongSoDongCanBo, setTongSoDongCanBo] = useState<number>(0);
    const [filterCanBoParams, setFilterCanBoParams] = useState<ReactQuery.ITimKiemPhanTrangTaiKhoanDaiLyParams>(defaultFilterCanBoParams);

    const [chiTietCanBoSelected, setChiTietCanBoSelected] = useState<CommonExecute.Execute.ITaiKhoanDaiLy | null>(null);

    const [isOpen, setIsOpen] = useState<boolean>(false);

    const refSearchInputTable = useRef<InputRef>(null);
    const [searchTextTable, setSearchTextTable] = useState<string>(""); //lưu textsearch để hiển thị vào cell
    const [searchedColumn, setSearchedColumn] = useState<TableCanBoKhaiThacColumnDataIndex | "">(""); //key column đang được search

    const [formTimCanBo] = Form.useForm();

    const closeModal = useCallback(() => {
      setIsOpen(false);
      // setChiTietCanBoSelected(null);// cho thằng này vào thì object chiTietCanBo ở ô select sẽ bị null ????
      formTimCanBo.resetFields();
    }, [formTimCanBo]);

    //Bấm tiếp theo
    const onPressXacNhan = useCallback(
      async (chiTietCanBoSelected: CommonExecute.Execute.ITaiKhoanDaiLy | null) => {
        try {
          // console.log("chiTietCanBoSelected", chiTietCanBoSelected);
          onSelectCanBoKhaiThac(chiTietCanBoSelected);
          closeModal();
        } catch (error) {
          console.log("onPressXacNhan error", error);
        }
      },
      [onSelectCanBoKhaiThac, closeModal],
    );

    /* CÁN BỘ */
    // TÌM KIẾM PHÂN TRANG CÁN BỘ
    const onPressSearchCanBo = useCallback(async () => {
      try {
        const response = await getListCanBoKhaiThac({
          ...filterCanBoParams,
        });
        if (response.data) {
          setListCanBoKhaiThac(response.data);
          setTongSoDongCanBo(response.tong_so_dong);
        }
      } catch (error) {
        console.log("onPressSearchCanBo error ", error);
      }
    }, [filterCanBoParams, getListCanBoKhaiThac]);

    useEffect(() => {
      onPressSearchCanBo();
    }, [filterCanBoParams]);

    const initData = async () => {
      try {
        setFilterCanBoParams({
          ...filterCanBoParams,
          ma_doi_tac: maDoiTacSelected,
          ma_dly: daiLyKhaiThacSelected,
        });
        formTimCanBo.setFields([
          {
            name: "ma_doi_tac",
            value: maDoiTacSelected,
          },
          {
            name: "ma_dly",
            value: daiLyKhaiThacSelected,
          },
        ]);
      } catch (error) {
        console.log("error", error);
      }
    };

    //MAP VALUE CỦA LIST VÀO TABLE
    const dataTableListCanBoKhaiThac = useMemo<Array<TableCanBoKhaiThacColumnDataType>>(() => {
      try {
        const tableData = listCanBoKhaiThac.map(itemCanBo => {
          return {
            key: itemCanBo.ma, // bắt buộc phải có key
            sott: itemCanBo.sott,
            ten: itemCanBo.ten,
            ma: itemCanBo.ma,
            ma_doi_tac: itemCanBo.ma_doi_tac,
            ma_dly: itemCanBo.ma_dly,
            ten_dly: itemCanBo.ten_dly,
          };
        });
        const arrEmptyRow: Array<TableCanBoKhaiThacColumnDataType> = fillRowTableEmpty(tableData.length, 10);
        return [...tableData, ...arrEmptyRow];
      } catch (error) {
        console.log("dataTableListCanBoKhaiThac error", error);
        return [];
      }
    }, [listCanBoKhaiThac]);

    const onSearchCanBo = (values: ReactQuery.ITimKiemPhanTrangCanBoQuanLyParams) => {
      setFilterCanBoParams({...values, trang: 1, so_dong: 10});
    };

    const handleSearch = useCallback((selectedKeys: string[], confirm: FilterDropdownProps["confirm"], dataIndex: TableCanBoKhaiThacColumnDataIndex) => {
      confirm();
      setSearchTextTable(selectedKeys[0]);
      setSearchedColumn(dataIndex);
    }, []);

    const handleReset = useCallback(
      (clearFilters: () => void, confirm: FilterDropdownProps["confirm"], dataIndex: TableCanBoKhaiThacColumnDataIndex) => {
        clearFilters();
        setSearchTextTable("");
        handleSearch([""], confirm, dataIndex);
      },
      [handleSearch],
    );

    // RENDER
    //FOOTER
    const renderFooter = () => {
      return (
        <Tooltip title={chiTietCanBoSelected ? "" : "Vui lòng chọn cán bộ"}>
          <Button type={"primary"} onClick={() => onPressXacNhan(chiTietCanBoSelected)} className="w-40" icon={<CheckCircleOutlined />} disabled={chiTietCanBoSelected ? false : true}>
            Chọn
          </Button>
        </Tooltip>
      );
    };
    const renderFormInputColum = (props: IFormInput) => (
      <Col span={4}>
        <FormInput {...props} />
      </Col>
    );

    // FORM TÌM KIẾM TABLE CÁN BỘ
    const renderHeaderTableQuanLyCanBo = () => (
      <Form
        form={formTimCanBo}
        initialValues={{}}
        layout="vertical" //Tất cả các input nằm trên 1 dòng
        onFinish={onSearchCanBo}
        className="[&_.ant-form-item]:mb-0">
        <Row gutter={16} align={"bottom"}>
          {renderFormInputColum({...ma_doi_tac, options: listDoiTac})}
          {renderFormInputColum(ma)}
          {renderFormInputColum({...ma_dly, disabled: true})}

          <Button type="primary" htmlType="submit" loading={loading} icon={<SearchOutlined />} iconPosition="end" className="!mb-2">
            Tìm kiếm
          </Button>
        </Row>
      </Form>
    );

    //lấy ra loại filter ở CELL HEADER tương ứng
    const getFilterTableCanBo = (dataIndex: string) => {
      if (dataIndex === "trang_thai_ten") return radioItemTrangThaiCanBoTable;
      return undefined;
    };

    // dataIndex : là các key của column, title : tiêu đề của column
    const getColumnSearchProps = (dataIndex: TableCanBoKhaiThacColumnDataIndex, title: string): TableColumnType<TableCanBoKhaiThacColumnDataType> => ({
      /**
       *  filterDropdown: UI tuỳ chỉnh của ô filter (component render ra trong dropdown filter)
       * @param param0
       * @returns
       * dataIndex !== "trang_thai_ten"
       */
      // filterDropdown:
      //   dataIndex !== "trang_thai_ten"
      //     ? filterDropdownParams => (
      //         <TableFilterDropdown
      //           ref={refSearchInputTable}
      //           title={title}
      //           dataIndex={dataIndex}
      //           handleSearch={(selectedKeys, confirm, dataIndex) => handleSearch(selectedKeys, confirm, dataIndex)}
      //           handleReset={(clearFilters, confirm, dataIndex) => clearFilters && handleReset(clearFilters, confirm, dataIndex)}
      //           {...filterDropdownParams}
      //         />
      //       )
      //     : undefined,
      // /**
      //  * filterIcon : icon hiển thị trên header column khi filter
      //  * @param filtered :  // "filtered". là boolean, true nếu đang áp dụng filter,
      //  *  biến này thay đổi khi hàm confirm được gọi, hoặc hàm clearFilters được gọi
      //  * @returns
      //  */
      filterIcon: (filtered: boolean) => {
        return <SearchOutlined style={{color: filtered ? "#1677ff" : undefined}} size={40} />;
      },
      /**
       * onFilter: hàm filter dữ liệu tại client-side (bắt buộc phải khai báo để filter hoạt động)
       * do hàm onFilter luôn phải trả về boolean, nên thêm || false vào để k bị lỗi
       * @param value : giá trị filter người dùng nhập vào
       * @param record : từng bản ghi trong dataSource
       * @returns
       */
      onFilter: (value, record) => {
        return (
          record[dataIndex]
            ?.toString()
            .toLowerCase()
            .includes((value as string).toLowerCase()) || false
        );
      },
      //filterDropdownProps: thuộc tính tuì chỉnh hành vi dropdown
      filterDropdownProps: {
        // onOpenChange: callback khi dropdown mở/ tắt thì sẽ focus vào input
        onOpenChange(open) {
          if (open) {
            setTimeout(() => refSearchInputTable.current?.select(), 100); // Focus/ select input khi mở filter
          }
        },
      },
      filterSearch: true,
      filters: getFilterTableCanBo(dataIndex),
      render: (
        text,
        record,
        //  index
      ) => {
        // if (dataIndex === "trang_thai_ten") {
        //   const color = text === "Đang sử dụng" ? COLOR_PALETTE.green[100] : COLOR_PALETTE.red[50];
        //   if (record.key.toString().includes("empty")) return "";
        //   return (
        //     <Tag color={color} className="text-[11px]">
        //       {text}
        //     </Tag>
        //   );
        // }
        return searchedColumn === dataIndex ? <Highlighter searchWords={[searchTextTable]} textToHighlight={text ? text.toString() : ""} /> : text !== undefined ? text : "\u00A0"; // xử lý chuyển text thành 1 dòng khi text quá dài
      },
    });

    const renderTable = () => (
      <Table<TableCanBoKhaiThacColumnDataType>
        {...defaultTableProps}
        dataSource={dataTableListCanBoKhaiThac} //mảng dữ liệu record được hiển thị
        columns={
          tableCanBoKhaiThacColumn?.map(item => {
            //Nếu là cột stt thì không hiển thị search
            return {...item, ...(item.key === "sott" ? {} : getColumnSearchProps(item.key as keyof TableCanBoKhaiThacColumnDataType, item.title as string))};
          }) || []
        } //định nghĩa cột của table
        rowClassName={record => (record.key === chiTietCanBoSelected?.ma ? "custom-row-selected" : "")} // xử lý việc 1 row được selected -> row đấy sẽ được highlight lên
        loading={loading} //hiển thị loading khi đang gọi API để loading data
        pagination={{
          ...defaultPaginationTableProps,
          defaultPageSize: 10,
          total: tongSoDongCanBo,
          onChange: (page, pageSize) => setFilterCanBoParams({...filterCanBoParams, trang: page, so_dong: pageSize}),
        }}
        title={renderHeaderTableQuanLyCanBo}
        onRow={record => {
          return {
            style: {cursor: loading ? "progress" : "pointer"}, //  thể hiện vị trí con trỏ là button(👈)
            onClick: () => setChiTietCanBoSelected(record as CommonExecute.Execute.ITaiKhoanDaiLy),
            onDoubleClick: () => onPressXacNhan(record as CommonExecute.Execute.ITaiKhoanDaiLy),
          };
        }}
      />
    );

    return (
      <Flex vertical gap="middle" align="flex-start">
        <Modal
          className="modal-tim-can-bo-hop-dong-con-nguoi"
          title="Chọn cán bộ"
          centered
          open={isOpen}
          onOk={() => closeModal()}
          onCancel={() => closeModal()}
          width={"65%"}
          styles={{
            body: {
              height: "70vh",
            },
          }}
          maskClosable={false}
          footer={renderFooter}>
          {renderTable()}
        </Modal>
      </Flex>
    );
  },
);

ModalTimCanBoKhaiThacComponent.displayName = "ModalTimCanBoKhaiThacComponent";
export const ModalTimCanBoKhaiThac = memo(ModalTimCanBoKhaiThacComponent, isEqual);
