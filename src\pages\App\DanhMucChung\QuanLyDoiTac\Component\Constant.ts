import {defaultTableColumnsProps} from "@src/hooks";
import {TableProps} from "antd";

export interface ChiTietDoiTacProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
}

//định ngh<PERSON> c<PERSON> h<PERSON>, biến mà modal sẽ expose để component bên ngo<PERSON><PERSON> sử dụng thông qua ref
export interface IModalChiTietDoiTacRef {
  open: (data?: CommonExecute.Execute.IDoiTac) => void;
  close: () => void;
}

export interface IModalChonLogoRef {
  open: (data?: CommonExecute.Execute.IDoiTac) => void;
  close: () => void;
}
export interface LogoProps {
  chiTietDoiTac: CommonExecute.Execute.IChiTietDoiTac | null;
  onClickChonFileSuccess?: (fileSelected: File.GetFolder.IGetFolder[]) => void;
}
//BẢNG LOGO
export interface TableLogoColumnDataType {
  key: string;
  stt?: number;
  id_file?: number;
  ten?: string;
  url_file?: string;
  extension?: string;
  loai?: string;
  hanh_dong?: () => JSX.Element | null;
}
export const tableLogoColumn: TableProps<TableLogoColumnDataType>["columns"] = [
  // {title: "STT", dataIndex: "stt", key: "stt", width: 60, align: "center", ...defaultTableColumnsProps},
  // {title: "Tên", dataIndex: "ten", key: "ten", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "Loại", dataIndex: "loai", key: "loai", width: 100, align: "center", ...defaultTableColumnsProps},
  {title: "URL file", dataIndex: "url_file", key: "url_file", align: "center", ...defaultTableColumnsProps},
  // {title: "Extension", dataIndex: "extension", key: "extension", width: 100, align: "center", ...defaultTableColumnsProps},
  {
    title: "Hành động",
    dataIndex: "hanh_dong",
    key: "hanh_dong",
    width: 200,
    align: "center",
    render: (_, record) => record.hanh_dong?.(),
    ...defaultTableColumnsProps,
  },
];
export type TableLogoColumnDataIndex = keyof TableLogoColumnDataType;
