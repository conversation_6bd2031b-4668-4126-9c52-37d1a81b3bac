#XAY_DUNG_GOI_BAO_HIEM {
  .antd-table-hide-scroll .ant-table-body {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
  }

  .antd-table-hide-scroll .ant-table-body::-webkit-scrollbar {
    display: none; /* Chrome, Safari */
  }

  .ant-table-container {
    // min-height: 620px;
    scrollbar-width: thin;
    scrollbar-color: #eaeaea transparent;
    scrollbar-gutter: stable;
  }
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
  .ant-form-item .ant-form-item-label {
    padding-bottom: 0px !important;

    label {
      font-size: 12px !important;
      font-weight: 600 !important;
    }
  }
}

.modal-them-goi-bao-hiem-con-nguoi {
  .ant-table-container {
    // min-height: 620px;
    scrollbar-width: thin;
    scrollbar-color: #eaeaea transparent;
    scrollbar-gutter: stable;
  }
  .ant-form-item .ant-form-item-control-input {
    min-height: 22px;
  }
  .ant-table-row:hover td {
    background-color: #e8f5e9 !important; //test
  }
  .no-header-border-radius .ant-table-thead > tr > th {
    border-radius: 0 !important;
  }
  .table-quyen-loi {
    .ant-table-container {
      max-height: calc(78vh - 44px - 26px - 141px); //78 : modal height, 44 : footer modal height, 26 : header modal height, 141 : form height
      overflow-x: hidden;
    }
  }
  .table-quyen-loi {
    .ant-table-title {
      padding: 0 !important;
    }
  }
}

.modal-them-quyen-loi {
  .tree-quyen-loi {
    max-height: calc(60vh - 32px); // 32px : search input,
    overflow-y: auto;
  }
}
