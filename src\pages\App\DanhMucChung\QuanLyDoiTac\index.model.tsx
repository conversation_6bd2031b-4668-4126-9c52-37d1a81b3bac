import {ReactQuery} from "@src/@types";
import {TableDoiTacColumnDataType} from "./index.configs";

export interface IQuanLyDoiTacContextProps {
  listDoiTac: Array<CommonExecute.Execute.IDoiTac>;
  tongSoDong: number;
  loading: boolean;
  filterParams: ReactQuery.ILayDanhSachDoiTacParams & ReactQuery.IPhanTrang;
  danhSachLogo: Array<CommonExecute.Execute.ILogoDoiTac>;
  LayDanhSachLogo: (ma_doi_tac: string) => Promise<Array<CommonExecute.Execute.ILogoDoiTac>>;
  getListDoiTac: (params?: ReactQuery.ILayDanhSachDoiTacParams & ReactQuery.IPhanTrang) => Promise<void>;
  getChiTietDoiTac: (params: TableDoiTacColumnDataType) => Promise<CommonExecute.Execute.IDoiTac>;
  capNhatChiTietDoiTac: (params: ReactQuery.IUpdateDoiTacParams) => Promise<any>;
  setFilterParams: React.Dispatch<React.SetStateAction<ReactQuery.ILayDanhSachDoiTacParams & ReactQuery.IPhanTrang>>;
}
